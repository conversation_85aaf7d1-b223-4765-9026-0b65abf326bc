// WrsEmployeeReportRepository: Repository for WrsEmployeeReport persistence
import { CatchRepositoryError } from '@/utils/repoDecorator'
import { WrsEmployeeReportMapper } from '../domain/mapper/WrsEmployeeReportMapper'
import { WrsEmployeeReportProps, WrsWorkItemProps } from '../domain/domain.schema'
import { Prisma, PrismaClient, WorkItemType } from "@prisma/client";
import { WrsEmployeeReport } from '../domain/WrsEmployeeReport';
import { EmployeeReportPaginationQuery, ListEmpReportQuery } from '../domain/query.schema';
import { EmployeeAggregation, StatisticsEmployeeReportResponse } from '../domain/response.schema';


export class WrsEmployeeReportRepository {
    constructor(private readonly prisma: PrismaClient) { }
    /**
     * 列出「所有人」週報統計(可透過query指定部門或員工)
     * @param params 查詢參數 { orgId?: string, weekId?: string, employeeId?: string }
     */
    @CatchRepositoryError()
    async statistics(params?: ListEmpReportQuery): Promise<StatisticsEmployeeReportResponse[]> {
        const { id, weekId, status, orgId } = params || {}
        //Warring: ACL TODO(Mike)
        const assigmentIds = await this.prisma.employee_assignment.findMany({
            where: {
                org_id: orgId,
                end_date: null,
            },
            select: { employee_id: true }
        })
        const empIds = assigmentIds.map(r => r.employee_id)
        const where: Prisma.wrs_employee_reportWhereInput = {
            ...id && { id },
            ...{
                org_id: orgId,
                employee_id: { in: empIds }
            },
            ...weekId && { week_id: weekId },
            ...status && { status },
        }
        const reports = await this.prisma.wrs_employee_report.findMany({
            where,
            orderBy: { submitted_at: 'desc' },
        })
        const reportIds = reports.map(r => r.id)

        const workItemStats = await this.prisma.wrs_work_item.groupBy({
            by: ['employee_report_id', 'type'],
            where: {
                status: { not: 'deleted' },
                ...weekId && { week_id: weekId },
                employee_report_id: {
                    in: reportIds,
                }
            },
            _count: {
                _all: true,
            }
        })
        const reportWithStats = assigmentIds.map(emp => {
            const report = reports.find(r => r.employee_id === emp.employee_id) || {
                id: '',
                org_id: orgId,
                week_id: weekId || '',
                status: 'draft',
                submitted_at: null,
                reviewed_by_supervisor: false,
            }

            const relatedStats = workItemStats.filter(
                w => w.employee_report_id === report.id
            )
            let total = 0
            return {
                employee_id: emp.employee_id,
                ...report,
                statistics: {
                    ...Object.fromEntries(
                        Object.keys(WorkItemType).map(type => {
                            const found = relatedStats.find(stat => stat.type === type);
                            total += found ? found._count._all : 0;
                            return [type, found ? found._count._all : 0];
                        })
                    ),
                    total
                }
            }
        })
        return reportWithStats.map(WrsEmployeeReportMapper.toStatistics);
    }
    /**
     * 查詢所有個人週報列表，支援分頁與條件
     * @param params 查詢參數
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async pagingList(params: EmployeeReportPaginationQuery, tx?: Prisma.TransactionClient): Promise<{ data: EmployeeAggregation[], total: number }> {
        const db = tx ?? this.prisma;
        const where: Prisma.wrs_employee_reportWhereInput = {};
        if (params.employeeId) where.employee_id = params.employeeId;
        if (params.weekId) where.week_id = params.weekId;
        if (params.status) where.status = params.status;

        const [data, total] = await Promise.all([
            db.wrs_employee_report.findMany({
                where,
                skip: params.skip ?? 0,
                take: params.take ?? 20,
                orderBy: { submitted_at: 'desc' },
                include: {
                    work_items: true
                }
            }),
            db.wrs_employee_report.count({ where })
        ]);

        return {
            data: data.map(WrsEmployeeReportMapper.toAggregationResponse),
            total
        };
    }

    /**
     * 建立個人週報草稿
     * @param props WrsEmployeeReportProps
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async create(props: WrsEmployeeReportProps, tx?: Prisma.TransactionClient): Promise<WrsEmployeeReport> {
        const db = tx ?? this.prisma;
        const created = await db.wrs_employee_report.create({
            data: WrsEmployeeReportMapper.toPersistence(props)
        });
        return WrsEmployeeReportMapper.toDomain(created);
    }

    /**
     * 查詢單一個人週報
     * @param id 週報ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async findById(id: string, tx?: Prisma.TransactionClient): Promise<EmployeeAggregation> {
        const db = tx ?? this.prisma;
        const found = await db.wrs_employee_report.findUnique({
            where: { id },
            include: {
                work_items: true
            }
        });

        if (!found) throw new Error('EmployeeReport not found');
        return WrsEmployeeReportMapper.toAggregationResponse(found);
    }
    /**
    * 查詢單一週報
    * @param tx Transaction client
    */
    @CatchRepositoryError()
    async findOne(params?: ListEmpReportQuery, tx?: Prisma.TransactionClient): Promise<EmployeeAggregation | null> {
        const db = tx ?? this.prisma;
        const { id, employeeId, orgId, weekId, status } = params || {}
        const where: Prisma.wrs_employee_reportWhereInput = {
            ...id && { id },
            ...employeeId && { employee_id: employeeId },
            ...orgId && { org_id: orgId },
            ...weekId && { week_id: weekId },
            ...status && { status },
        }
        const report = await db.wrs_employee_report.findFirst({
            where,
            include: {
                work_items: true
            },
            orderBy: [
                { id: 'desc' },
                { submitted_at: 'desc' }
            ],
        })
        if (!report) {
            return null;
        }
        return WrsEmployeeReportMapper.toAggregationResponse(report);
    }

    /**
     * 更新個人週報草稿
     * @param id 週報ID
     * @param updateProps 更新內容
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async updateDraft(id: string, updateProps: Partial<WrsEmployeeReportProps>, tx?: Prisma.TransactionClient): Promise<WrsEmployeeReport> {
        const db = tx ?? this.prisma;
        const updated = await db.wrs_employee_report.update({
            where: { id },
            data: updateProps
        });
        return WrsEmployeeReportMapper.toDomain(updated);
    }

    /**
     * 提交個人週報
     * @param id 週報ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async submit(id: string, tx?: Prisma.TransactionClient): Promise<WrsEmployeeReport> {
        const db = tx ?? this.prisma;
        const now = new Date().toISOString();
        const updated = await db.wrs_employee_report.update({
            where: { id },
            data: { status: 'submitted', submitted_at: now }
        });
        return WrsEmployeeReportMapper.toDomain(updated);
    }

    @CatchRepositoryError()
    async submitByUserWeekId(userId: string, weekId: string, tx?: Prisma.TransactionClient): Promise<WrsEmployeeReport> {
        const db = tx ?? this.prisma;
        const now = new Date().toISOString();
        const where: Prisma.wrs_employee_reportWhereUniqueInput = {
            week_id_employee_id: {
              week_id: weekId,
              employee_id: userId
            }
          }
        const updated = await db.wrs_employee_report.update({
            where,
            data: { status: 'submitted', submitted_at: now }
        });
        return WrsEmployeeReportMapper.toDomain(updated);
    }
}
