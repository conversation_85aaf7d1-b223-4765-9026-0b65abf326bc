import { PaginationListQuery } from "@/module/common/query.schema";
import { WrsDepartmentReportPropsSchema, WrsEmployeeReportPropsSchema, WrsWeekPropsSchema, WrsWorkItemPropsSchema } from "./domain.schema";
import { z } from "zod";

export const DepartmentReportPaginationQuerySchema=PaginationListQuery(WrsDepartmentReportPropsSchema)
export type DepartmentReportPaginationQuery= z.infer<typeof DepartmentReportPaginationQuerySchema>;

export const EmployeeReportPaginationQuerySchema=PaginationListQuery(WrsEmployeeReportPropsSchema)
export type EmployeeReportPaginationQuery= z.infer<typeof EmployeeReportPaginationQuerySchema>;

export const WorkItemPaginationQuerySchema=PaginationListQuery(WrsWorkItemPropsSchema)
export type WorkItemPaginationQuery= z.infer<typeof WorkItemPaginationQuerySchema>;

export const MyWorkItemPaginationQuerySchema=PaginationListQuery(WrsWorkItemPropsSchema.pick({
  weekId: true,
}))
export type MyWorkItemPaginationQuery= z.infer<typeof MyWorkItemPaginationQuerySchema>;

export const MyOrgWorkItemPaginationQuerySchema=PaginationListQuery(WrsWorkItemPropsSchema.pick({
  employeeId: true,
  weekId: true,
}))
export type MyOrgWorkItemPaginationQuery= z.infer<typeof MyOrgWorkItemPaginationQuerySchema>;

export const WeekPaginationQuerySchema=PaginationListQuery(WrsWeekPropsSchema)
export type WeekPaginationQuery= z.infer<typeof WeekPaginationQuerySchema>;

export const ListEmpReportQuerySchema=WrsEmployeeReportPropsSchema.pick({
  id: true,
  employeeId: true,
  orgId: true,
  weekId: true,
  status: true,
}).partial().describe('員工週報查詢參數，包含id、employeeId、orgId、weekId、status等欄位')
export type ListEmpReportQuery= z.infer<typeof ListEmpReportQuerySchema>;
export const ListDepartmentReportQuerySchema=WrsDepartmentReportPropsSchema.pick({
  id: true,
  weekId: true,
  status: true,     
}).partial().describe('部門週報查詢參數，包含id、orgId、weekId、status等欄位')
export type ListDepartmentReportQuery= z.infer<typeof ListDepartmentReportQuerySchema>;