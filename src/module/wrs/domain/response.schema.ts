import { z } from "zod";
import {  WrsDepartmentReportPropsSchema, WrsEmployeeReportPropsSchema, WrsWorkItemPropsSchema } from "./domain.schema";

export const NumberOfItemsSchema = z.object({
  total: z.number().int().min(0).describe('工作項目數量統計，表示該週的總工作項目總數'),
  project: z.number().int().min(0).describe('專案工作項目數量'),
  routine: z.number().int().min(0).describe('例行工作項目數量'),
  support: z.number().int().min(0).describe('支援工作項目數量'),
  admin: z.number().int().min(0).describe('行政工作項目數量'),
  training: z.number().int().min(0).describe('培訓工作項目數量'),
  other: z.number().int().min(0).describe('其他工作項目數量')
})

export const StatisticsEmployeeReportResponseSchema = WrsEmployeeReportPropsSchema.extend({
  statistics: NumberOfItemsSchema,
});
export type StatisticsEmployeeReportResponse = Partial<z.infer<typeof StatisticsEmployeeReportResponseSchema>>;
export const StatisticsDepartmentReportResponseSchema = WrsDepartmentReportPropsSchema.extend({
  statistics: NumberOfItemsSchema,
});
export type StatisticsDepartmentReportResponse =Partial<z.infer<typeof StatisticsDepartmentReportResponseSchema>>;

export const EmployeeAggregationSchema = WrsEmployeeReportPropsSchema.extend({
  items: z.array(WrsWorkItemPropsSchema).describe('工作項目列表，包含該週報的所有工作項目')
})
export type EmployeeAggregation = z.infer<typeof EmployeeAggregationSchema>;